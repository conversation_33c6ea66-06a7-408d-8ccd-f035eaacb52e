#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { Command } = require('commander');
const RuntimePageChecker = require('../src/runtime-validation/RuntimePageChecker');

const program = new Command();

program
  .name('page-validator')
  .description('Vue 2 到 Vue 3 迁移的页面运行时验证工具')
  .version('1.0.0');

program
  .command('check')
  .description('检查项目中所有页面的运行时状态')
  .argument('[project-path]', '项目路径', process.cwd())
  .option('-p, --port <port>', '开发服务器端口', '3000')
  .option('-t, --timeout <timeout>', '页面加载超时时间(ms)', '30000')
  .option('--page-timeout <timeout>', '单个页面超时时间(ms)', '5000')
  .option('--wait-server <timeout>', '等待服务器启动时间(ms)', '10000')
  .option('--dev-command <command>', '开发服务器启动命令')
  .option('--base-url <url>', '使用外部服务器地址（跳过启动开发服务器）')
  .option('--headless', '无头模式运行浏览器', true)
  .option('--no-headless', '显示浏览器界面')
  .option('--auto-fix', '启用自动错误修复')
  .option('--max-fix-attempts <attempts>', '最大修复尝试次数', '3')
  .option('--no-ai', '禁用 AI 功能')
  .option('--output-dir <dir>', '报告输出目录')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('-v, --verbose', '详细输出')
  .action(async (projectPath, options) => {
    try {
      await runPageValidation(projectPath, options);
    } catch (error) {
      console.error(chalk.red(`❌ 执行失败: ${error.message}`));
      process.exit(1);
    }
  });

program
  .command('parse-routes')
  .description('仅解析项目路由，不进行页面验证')
  .argument('[project-path]', '项目路径', process.cwd())
  .option('--no-ai', '禁用 AI 功能')
  .option('-v, --verbose', '详细输出')
  .action(async (projectPath, options) => {
    try {
      await runRouteParsingOnly(projectPath, options);
    } catch (error) {
      console.error(chalk.red(`❌ 路由解析失败: ${error.message}`));
      process.exit(1);
    }
  });

program
  .command('validate-url')
  .description('验证单个 URL')
  .argument('<url>', '要验证的 URL')
  .option('-t, --timeout <timeout>', '页面加载超时时间(ms)', '30000')
  .option('--headless', '无头模式运行浏览器', true)
  .option('--no-headless', '显示浏览器界面')
  .option('-v, --verbose', '详细输出')
  .action(async (url, options) => {
    try {
      await runSingleUrlValidation(url, options);
    } catch (error) {
      console.error(chalk.red(`❌ URL 验证失败: ${error.message}`));
      process.exit(1);
    }
  });

program
  .command('report')
  .description('查看最新的验证报告')
  .argument('[project-path]', '项目路径', process.cwd())
  .option('--output-dir <dir>', '报告目录')
  .action(async (projectPath, options) => {
    try {
      await showLatestReport(projectPath, options);
    } catch (error) {
      console.error(chalk.red(`❌ 查看报告失败: ${error.message}`));
      process.exit(1);
    }
  });

/**
 * 执行完整的页面验证
 */
async function runPageValidation(projectPath, options) {
  console.log(chalk.blue('🚀 Vue 页面运行时验证工具'));
  console.log(chalk.gray(`   项目路径: ${path.resolve(projectPath)}`));

  // 验证项目路径
  if (!await fs.pathExists(projectPath)) {
    throw new Error(`项目路径不存在: ${projectPath}`);
  }

  // 检查是否是 Vue 项目
  const packageJsonPath = path.join(projectPath, 'package.json');
  if (!await fs.pathExists(packageJsonPath)) {
    throw new Error('未找到 package.json，请确认这是一个有效的项目目录');
  }

  const packageJson = await fs.readJson(packageJsonPath);
  if (!packageJson.dependencies?.vue && !packageJson.devDependencies?.vue) {
    console.log(chalk.yellow('⚠️  未检测到 Vue 依赖，但将继续执行验证'));
  }

  // 创建页面检查器
  const checker = new RuntimePageChecker(projectPath, {
    port: parseInt(options.port),
    timeout: parseInt(options.timeout),
    pageTimeout: parseInt(options.pageTimeout),
    waitForServer: parseInt(options.waitServer),
    devCommand: options.devCommand,
    baseUrl: options.baseUrl,
    headless: options.headless,
    autoFix: options.autoFix,
    maxFixAttempts: parseInt(options.maxFixAttempts),
    useAI: !options.noAi,
    outputDir: options.outputDir,
    dryRun: options.dryRun,
    verbose: options.verbose
  });

  // 执行检查
  const result = await checker.checkAllPages();

  if (result.success) {
    console.log(chalk.green('\n🎉 页面验证完成！'));
    
    const validation = result.results.validation;
    if (validation?.report) {
      const successRate = parseFloat(validation.report.summary.successRate);
      if (successRate === 100) {
        console.log(chalk.green('✨ 所有页面都运行正常！'));
      } else if (successRate >= 80) {
        console.log(chalk.yellow(`⚠️  ${validation.report.summary.failed} 个页面需要修复`));
      } else {
        console.log(chalk.red(`🚨 发现较多问题，建议优先修复关键页面`));
      }
    }
  } else {
    console.log(chalk.red('\n❌ 页面验证失败'));
    process.exit(1);
  }
}

/**
 * 仅执行路由解析
 */
async function runRouteParsingOnly(projectPath, options) {
  console.log(chalk.blue('🔍 Vue 路由解析工具'));
  console.log(chalk.gray(`   项目路径: ${path.resolve(projectPath)}`));

  const RouteParser = require('../src/runtime-validation/RouteParser');
  
  const parser = new RouteParser(projectPath, {
    useAI: !options.noAi,
    verbose: options.verbose
  });

  const result = await parser.parseRoutes();

  if (result.success) {
    const routes = parser.getRoutePaths();
    console.log(chalk.green(`\n✅ 成功解析 ${routes.length} 个路由:`));
    
    for (const route of routes) {
      console.log(chalk.gray(`   ${route.path}${route.name ? ` (${route.name})` : ''}`));
    }
  } else {
    console.log(chalk.red('\n❌ 路由解析失败'));
    if (result.errors.length > 0) {
      console.log(chalk.red('错误信息:'));
      for (const error of result.errors) {
        console.log(chalk.red(`   - ${error}`));
      }
    }
    process.exit(1);
  }
}

/**
 * 验证单个 URL
 */
async function runSingleUrlValidation(url, options) {
  console.log(chalk.blue('🔍 单个 URL 验证'));
  console.log(chalk.gray(`   URL: ${url}`));

  const PageValidator = require('../src/runtime-validation/PageValidator');
  
  // 创建一个虚拟路由
  const routes = [{ path: new URL(url).pathname, name: 'SingleURL' }];
  
  const validator = new PageValidator(process.cwd(), routes, {
    baseUrl: new URL(url).origin,
    timeout: parseInt(options.timeout),
    headless: options.headless,
    verbose: options.verbose
  });

  const result = await validator.validateAllPages();

  if (result.success && result.results.length > 0) {
    const pageResult = result.results[0];
    
    if (pageResult.success) {
      console.log(chalk.green(`\n✅ URL 验证成功`));
      console.log(chalk.gray(`   加载时间: ${pageResult.loadTime}ms`));
    } else {
      console.log(chalk.red(`\n❌ URL 验证失败`));
      console.log(chalk.red('错误信息:'));
      for (const error of pageResult.errors) {
        console.log(chalk.red(`   - ${error}`));
      }
    }
    
    if (pageResult.warnings.length > 0) {
      console.log(chalk.yellow('警告信息:'));
      for (const warning of pageResult.warnings) {
        console.log(chalk.yellow(`   - ${warning}`));
      }
    }
  } else {
    console.log(chalk.red('\n❌ URL 验证失败'));
    process.exit(1);
  }
}

/**
 * 显示最新报告
 */
async function showLatestReport(projectPath, options) {
  const outputDir = options.outputDir || path.join(projectPath, 'validation-reports');
  const summaryPath = path.join(outputDir, 'latest-summary.md');

  if (!await fs.pathExists(summaryPath)) {
    console.log(chalk.yellow('⚠️  未找到验证报告，请先运行页面验证'));
    return;
  }

  const summary = await fs.readFile(summaryPath, 'utf8');
  console.log(summary);
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error(chalk.red(`❌ 未捕获的异常: ${error.message}`));
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red(`❌ 未处理的 Promise 拒绝: ${reason}`));
  process.exit(1);
});

// 优雅退出
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n⚠️  收到中断信号，正在清理资源...'));
  process.exit(0);
});

program.parse();
