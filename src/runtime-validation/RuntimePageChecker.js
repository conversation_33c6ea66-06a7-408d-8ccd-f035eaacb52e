const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const RouteParser = require('./RouteParser');
const PageValidator = require('./PageValidator');
const RuntimeErrorHandler = require('../build-time-repair/RuntimeErrorHandler');

/**
 * RuntimePageChecker - 运行时页面检查器主控制器
 * 
 * 功能：
 * 1. 协调路由解析和页面验证
 * 2. 集成运行时错误处理
 * 3. 提供统一的检查接口
 * 4. 生成完整的检查报告
 */
class RuntimePageChecker {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      // 路由解析选项
      useAI: true,
      routerPaths: [
        'src/router/index.js',
        'src/router/index.ts',
        'router/index.js',
        'router/index.ts',
        'src/router.js',
        'src/router.ts'
      ],

      // 页面验证选项
      port: 3000,
      timeout: 30000,
      headless: true,
      devCommand: null,
      baseUrl: null,
      pageTimeout: 5000,
      waitForServer: 10000,

      // 错误处理选项
      autoFix: false,
      maxFixAttempts: 3,

      // 通用选项
      verbose: false,
      outputDir: null,
      dryRun: false,

      ...options
    };

    // 自动检测开发命令
    if (!this.options.devCommand) {
      this.options.devCommand = this.detectDevCommand();
    }

    // 设置输出目录
    if (!this.options.outputDir) {
      this.options.outputDir = path.join(this.projectPath, 'validation-reports');
    }

    this.routeParser = null;
    this.pageValidator = null;
    this.runtimeErrorHandler = null;
    this.checkResults = {
      routes: [],
      validation: null,
      errors: [],
      timestamp: null
    };
  }

  /**
   * 执行完整的页面检查流程
   */
  async checkAllPages() {
    console.log(chalk.blue('🚀 开始运行时页面检查...'));
    console.log(chalk.gray(`   项目路径: ${this.projectPath}`));
    
    const startTime = Date.now();
    this.checkResults.timestamp = new Date().toISOString();

    try {
      // 1. 解析路由
      console.log(chalk.blue('\n📍 步骤 1: 解析项目路由'));
      const routeResult = await this.parseRoutes();
      
      if (!routeResult.success || routeResult.routes.length === 0) {
        throw new Error('路由解析失败或未找到有效路由');
      }

      this.checkResults.routes = routeResult.routes;
      console.log(chalk.green(`✅ 找到 ${routeResult.routes.length} 个路由`));

      // 2. 设置运行时错误处理（如果启用）
      if (this.options.autoFix) {
        console.log(chalk.blue('\n🔧 步骤 2: 设置运行时错误处理'));
        await this.setupRuntimeErrorHandler();
      }

      // 3. 验证页面
      console.log(chalk.blue('\n🔍 步骤 3: 验证页面'));
      const validationResult = await this.validatePages(routeResult.routes);
      
      this.checkResults.validation = validationResult;

      // 4. 生成报告
      console.log(chalk.blue('\n📄 步骤 4: 生成检查报告'));
      await this.generateReports();

      const duration = Date.now() - startTime;
      console.log(chalk.green(`\n✅ 页面检查完成，耗时 ${duration}ms`));
      
      this.printFinalSummary();

      return {
        success: true,
        results: this.checkResults,
        duration: duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(chalk.red(`\n❌ 页面检查失败: ${error.message}`));
      
      this.checkResults.errors.push(error.message);

      return {
        success: false,
        results: this.checkResults,
        error: error.message,
        duration: duration
      };
    }
  }

  /**
   * 解析项目路由
   */
  async parseRoutes() {
    this.routeParser = new RouteParser(this.projectPath, {
      verbose: this.options.verbose,
      useAI: this.options.useAI,
      routerPaths: this.options.routerPaths
    });

    const result = await this.routeParser.parseRoutes();
    
    if (result.success) {
      const routePaths = this.routeParser.getRoutePaths();
      return {
        success: true,
        routes: routePaths,
        errors: result.errors
      };
    }

    return result;
  }

  /**
   * 设置运行时错误处理器
   */
  async setupRuntimeErrorHandler() {
    this.runtimeErrorHandler = new RuntimeErrorHandler(this.projectPath, {
      port: this.options.port,
      autoFix: this.options.autoFix,
      maxAttempts: this.options.maxFixAttempts,
      verbose: this.options.verbose
    });

    // 这里可以添加更多的错误处理器设置
    if (this.options.verbose) {
      console.log(chalk.gray('   运行时错误处理器已设置'));
    }
  }

  /**
   * 验证所有页面
   */
  async validatePages(routes) {
    this.pageValidator = new PageValidator(this.projectPath, routes, {
      port: this.options.port,
      timeout: this.options.timeout,
      headless: this.options.headless,
      devCommand: this.options.devCommand,
      baseUrl: this.options.baseUrl,
      pageTimeout: this.options.pageTimeout,
      waitForServer: this.options.waitForServer,
      autoFix: this.options.autoFix,
      verbose: this.options.verbose
    });

    return await this.pageValidator.validateAllPages();
  }

  /**
   * 生成检查报告
   */
  async generateReports() {
    // 确保输出目录存在
    await fs.ensureDir(this.options.outputDir);

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // 生成 Markdown 报告
    const markdownPath = path.join(this.options.outputDir, `page-validation-${timestamp}.md`);
    if (this.pageValidator) {
      await this.pageValidator.saveReport(markdownPath);
    }

    // 生成 JSON 报告
    const jsonPath = path.join(this.options.outputDir, `page-validation-${timestamp}.json`);
    await fs.writeJson(jsonPath, this.checkResults, { spaces: 2 });
    
    console.log(chalk.green(`📄 JSON 报告已保存: ${jsonPath}`));

    // 生成摘要报告
    const summaryPath = path.join(this.options.outputDir, 'latest-summary.md');
    await this.generateSummaryReport(summaryPath);
  }

  /**
   * 生成摘要报告
   */
  async generateSummaryReport(outputPath) {
    const validation = this.checkResults.validation;
    if (!validation) return;

    let summary = `# 页面检查摘要\n\n`;
    summary += `检查时间: ${this.checkResults.timestamp}\n`;
    summary += `项目路径: ${this.projectPath}\n\n`;

    summary += `## 路由统计\n\n`;
    summary += `- 总路由数: ${this.checkResults.routes.length}\n`;
    summary += `- 验证页面数: ${validation.report?.summary.total || 0}\n\n`;

    summary += `## 验证结果\n\n`;
    if (validation.report) {
      summary += `- 成功: ${validation.report.summary.successful}\n`;
      summary += `- 失败: ${validation.report.summary.failed}\n`;
      summary += `- 成功率: ${validation.report.summary.successRate}%\n\n`;
    }

    if (validation.report?.failedPages?.length > 0) {
      summary += `## 主要问题\n\n`;
      const topErrors = this.getTopErrors(validation.report.failedPages);
      for (const [error, count] of topErrors) {
        summary += `- ${error} (${count} 次)\n`;
      }
      summary += `\n`;
    }

    summary += `## 建议\n\n`;
    summary += this.generateRecommendations();

    await fs.writeFile(outputPath, summary, 'utf8');
    console.log(chalk.green(`📄 摘要报告已保存: ${outputPath}`));
  }

  /**
   * 获取最常见的错误
   */
  getTopErrors(failedPages) {
    const errorCounts = new Map();
    
    for (const page of failedPages) {
      for (const error of page.errors) {
        // 简化错误信息
        const simplifiedError = error.split(':')[0].trim();
        errorCounts.set(simplifiedError, (errorCounts.get(simplifiedError) || 0) + 1);
      }
    }

    // 按出现次数排序，返回前5个
    return Array.from(errorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);
  }

  /**
   * 生成建议
   */
  generateRecommendations() {
    const validation = this.checkResults.validation;
    let recommendations = '';

    if (!validation || !validation.report) {
      return '暂无建议。\n';
    }

    const { successful, failed, total } = validation.report.summary;
    const successRate = parseFloat(validation.report.summary.successRate);

    if (successRate === 100) {
      recommendations += '🎉 所有页面都通过了验证！项目运行状态良好。\n\n';
    } else if (successRate >= 80) {
      recommendations += '✅ 大部分页面运行正常，建议修复剩余的问题。\n\n';
    } else if (successRate >= 50) {
      recommendations += '⚠️  有较多页面存在问题，建议优先修复关键页面。\n\n';
    } else {
      recommendations += '🚨 项目存在严重问题，建议立即进行全面检查和修复。\n\n';
    }

    // 基于常见错误类型给出建议
    if (validation.report.failedPages) {
      const topErrors = this.getTopErrors(validation.report.failedPages);
      
      for (const [error, count] of topErrors) {
        if (error.includes('Console Error')) {
          recommendations += `- 修复 JavaScript 控制台错误 (${count} 个页面受影响)\n`;
        } else if (error.includes('Network Error')) {
          recommendations += `- 检查网络请求和资源加载问题 (${count} 个页面受影响)\n`;
        } else if (error.includes('HTTP')) {
          recommendations += `- 修复 HTTP 状态码错误 (${count} 个页面受影响)\n`;
        } else if (error.includes('Page Error')) {
          recommendations += `- 修复页面 JavaScript 运行时错误 (${count} 个页面受影响)\n`;
        }
      }
    }

    if (this.options.autoFix) {
      recommendations += `\n💡 提示: 已启用自动修复功能，部分错误可能会被自动处理。\n`;
    } else {
      recommendations += `\n💡 提示: 可以启用 --auto-fix 选项来尝试自动修复部分错误。\n`;
    }

    return recommendations;
  }

  /**
   * 打印最终摘要
   */
  printFinalSummary() {
    const validation = this.checkResults.validation;
    if (!validation || !validation.report) return;

    console.log(chalk.blue('\n📊 最终检查结果:'));
    console.log(chalk.gray(`   路由总数: ${this.checkResults.routes.length}`));
    console.log(chalk.gray(`   验证页面: ${validation.report.summary.total}`));
    console.log(chalk.green(`   成功: ${validation.report.summary.successful}`));
    console.log(chalk.red(`   失败: ${validation.report.summary.failed}`));
    console.log(chalk.blue(`   成功率: ${validation.report.summary.successRate}%`));

    if (validation.report.summary.failed > 0) {
      console.log(chalk.yellow(`\n⚠️  发现 ${validation.report.summary.failed} 个页面存在问题`));
      console.log(chalk.gray(`   详细报告已保存到: ${this.options.outputDir}`));
    }
  }

  /**
   * 自动检测开发命令
   */
  detectDevCommand() {
    const packageJsonPath = path.join(this.projectPath, 'package.json');
    
    try {
      if (fs.existsSync(packageJsonPath)) {
        const packageJson = fs.readJsonSync(packageJsonPath);
        const scripts = packageJson.scripts || {};
        
        // 按优先级检查常见的开发命令
        const devCommands = ['dev', 'serve', 'start', 'development'];
        
        for (const cmd of devCommands) {
          if (scripts[cmd]) {
            return `npm run ${cmd}`;
          }
        }
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  检测开发命令失败: ${error.message}`));
      }
    }

    // 默认命令
    return 'npm run dev';
  }

  /**
   * 获取检查结果
   */
  getResults() {
    return this.checkResults;
  }

  /**
   * 清理资源
   */
  async cleanup() {
    if (this.pageValidator) {
      await this.pageValidator.cleanup();
    }
  }
}

module.exports = RuntimePageChecker;
