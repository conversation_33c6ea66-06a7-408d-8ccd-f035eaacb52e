const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { parse } = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const { AIService } = require('../ai/ai-service');

/**
 * RouteParser - Vue Router 路由解析器
 * 
 * 功能：
 * 1. 静态解析 router/ 目录下的路由配置文件
 * 2. 支持 Vue 2 和 Vue 3 的路由格式
 * 3. 当静态解析失败时，使用 AI 分析代码
 * 4. 提取所有可访问的路由路径
 */
class RouteParser extends AIService {
  constructor(projectPath, options = {}) {
    super(options);
    
    this.projectPath = projectPath;
    this.options = {
      verbose: false,
      useAI: true,
      routerPaths: [
        'src/router/index.js',
        'src/router/index.ts', 
        'router/index.js',
        'router/index.ts',
        'src/router.js',
        'src/router.ts'
      ],
      ...options
    };

    this.routes = [];
    this.routerFiles = [];
    this.parseErrors = [];
  }

  /**
   * 解析项目中的所有路由
   */
  async parseRoutes() {
    console.log(chalk.blue('🔍 开始解析项目路由...'));

    try {
      // 1. 查找路由文件
      await this.findRouterFiles();
      
      if (this.routerFiles.length === 0) {
        throw new Error('未找到路由配置文件');
      }

      // 2. 解析每个路由文件
      for (const routerFile of this.routerFiles) {
        await this.parseRouterFile(routerFile);
      }

      // 3. 如果静态解析失败且启用AI，尝试AI解析
      if (this.routes.length === 0 && this.options.useAI && this.isEnabled()) {
        console.log(chalk.yellow('⚠️  静态解析失败，尝试使用 AI 解析...'));
        await this.parseWithAI();
      }

      console.log(chalk.green(`✅ 路由解析完成，共找到 ${this.routes.length} 个路由`));
      
      if (this.options.verbose) {
        this.printRoutes();
      }

      return {
        success: true,
        routes: this.routes,
        routerFiles: this.routerFiles,
        errors: this.parseErrors
      };

    } catch (error) {
      console.error(chalk.red(`❌ 路由解析失败: ${error.message}`));
      return {
        success: false,
        routes: [],
        routerFiles: this.routerFiles,
        errors: [...this.parseErrors, error.message]
      };
    }
  }

  /**
   * 查找路由配置文件
   */
  async findRouterFiles() {
    for (const routerPath of this.options.routerPaths) {
      const fullPath = path.join(this.projectPath, routerPath);
      
      if (await fs.pathExists(fullPath)) {
        this.routerFiles.push({
          path: routerPath,
          fullPath: fullPath
        });
        
        if (this.options.verbose) {
          console.log(chalk.gray(`   找到路由文件: ${routerPath}`));
        }
      }
    }

    // 递归查找 router 目录下的其他文件
    const routerDir = path.join(this.projectPath, 'src/router');
    if (await fs.pathExists(routerDir)) {
      await this.findAdditionalRouterFiles(routerDir);
    }
  }

  /**
   * 递归查找额外的路由文件
   */
  async findAdditionalRouterFiles(dir) {
    try {
      const files = await fs.readdir(dir);
      
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = await fs.stat(filePath);
        
        if (stat.isDirectory()) {
          await this.findAdditionalRouterFiles(filePath);
        } else if (file.match(/\.(js|ts)$/) && !file.includes('index')) {
          const relativePath = path.relative(this.projectPath, filePath);
          this.routerFiles.push({
            path: relativePath,
            fullPath: filePath
          });
          
          if (this.options.verbose) {
            console.log(chalk.gray(`   找到额外路由文件: ${relativePath}`));
          }
        }
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  读取目录失败 ${dir}: ${error.message}`));
      }
    }
  }

  /**
   * 解析单个路由文件
   */
  async parseRouterFile(routerFile) {
    try {
      const content = await fs.readFile(routerFile.fullPath, 'utf8');
      
      if (this.options.verbose) {
        console.log(chalk.gray(`   解析文件: ${routerFile.path}`));
      }

      // 尝试静态解析
      const staticRoutes = await this.staticParseRoutes(content, routerFile.path);
      
      if (staticRoutes.length > 0) {
        this.routes.push(...staticRoutes);
      } else {
        this.parseErrors.push(`静态解析失败: ${routerFile.path}`);
      }

    } catch (error) {
      const errorMsg = `解析文件失败 ${routerFile.path}: ${error.message}`;
      this.parseErrors.push(errorMsg);
      
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  ${errorMsg}`));
      }
    }
  }

  /**
   * 静态解析路由配置
   */
  async staticParseRoutes(content, filePath) {
    try {
      // 使用 Babel 解析 JavaScript/TypeScript
      const ast = parse(content, {
        sourceType: 'module',
        plugins: [
          'jsx',
          'typescript',
          'decorators-legacy',
          'classProperties',
          'objectRestSpread',
          'asyncGenerators',
          'functionBind',
          'exportDefaultFrom',
          'exportNamespaceFrom',
          'dynamicImport'
        ]
      });

      const routes = [];
      const self = this; // 保存 this 上下文

      // 遍历 AST 查找路由定义
      traverse(ast, {
        // 查找 routes 数组定义
        VariableDeclarator(path) {
          if (path.node.id.name === 'routes' && path.node.init) {
            const routeArray = self.extractRoutesFromArray(path.node.init);
            routes.push(...routeArray);
          }
        },

        // 查找 createRouter 调用
        CallExpression(path) {
          if (path.node.callee.name === 'createRouter' ||
              (path.node.callee.property && path.node.callee.property.name === 'createRouter')) {

            const routesProperty = path.node.arguments[0]?.properties?.find(
              prop => prop.key.name === 'routes'
            );

            if (routesProperty) {
              const routeArray = self.extractRoutesFromArray(routesProperty.value);
              routes.push(...routeArray);
            }
          }
        },

        // 查找 new VueRouter 调用 (Vue 2)
        NewExpression(path) {
          if (path.node.callee.name === 'VueRouter' ||
              (path.node.callee.property && path.node.callee.property.name === 'VueRouter')) {

            const routesProperty = path.node.arguments[0]?.properties?.find(
              prop => prop.key.name === 'routes'
            );

            if (routesProperty) {
              const routeArray = self.extractRoutesFromArray(routesProperty.value);
              routes.push(...routeArray);
            }
          }
        }
      });

      return routes;

    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  静态解析失败 ${filePath}: ${error.message}`));
      }
      return [];
    }
  }

  /**
   * 从数组节点中提取路由信息
   */
  extractRoutesFromArray(arrayNode) {
    if (!arrayNode || arrayNode.type !== 'ArrayExpression') {
      return [];
    }

    const routes = [];
    
    for (const element of arrayNode.elements) {
      if (element && element.type === 'ObjectExpression') {
        const route = this.extractRouteFromObject(element);
        if (route) {
          routes.push(route);
        }
      }
    }

    return routes;
  }

  /**
   * 从对象节点中提取单个路由信息
   */
  extractRouteFromObject(objectNode) {
    const route = {
      path: null,
      name: null,
      component: null,
      meta: {},
      children: []
    };

    for (const property of objectNode.properties) {
      if (property.key.type === 'Identifier') {
        const key = property.key.name;
        
        switch (key) {
          case 'path':
            if (property.value.type === 'StringLiteral') {
              route.path = property.value.value;
            }
            break;
            
          case 'name':
            if (property.value.type === 'StringLiteral') {
              route.name = property.value.value;
            }
            break;
            
          case 'component':
            route.component = this.extractComponentInfo(property.value);
            break;
            
          case 'meta':
            if (property.value.type === 'ObjectExpression') {
              route.meta = this.extractMetaInfo(property.value);
            }
            break;
            
          case 'children':
            if (property.value.type === 'ArrayExpression') {
              route.children = this.extractRoutesFromArray(property.value);
            }
            break;
        }
      }
    }

    // 只返回有效的路由（至少有 path）
    return route.path ? route : null;
  }

  /**
   * 提取组件信息
   */
  extractComponentInfo(valueNode) {
    if (valueNode.type === 'Identifier') {
      return { type: 'imported', name: valueNode.name };
    } else if (valueNode.type === 'ArrowFunctionExpression' || valueNode.type === 'FunctionExpression') {
      // 动态导入组件
      return { type: 'dynamic', source: 'function' };
    } else if (valueNode.type === 'CallExpression') {
      // import() 调用
      if (valueNode.callee.type === 'Import' && valueNode.arguments[0]?.type === 'StringLiteral') {
        return { type: 'dynamic', source: valueNode.arguments[0].value };
      }
    }
    
    return { type: 'unknown' };
  }

  /**
   * 提取 meta 信息
   */
  extractMetaInfo(objectNode) {
    const meta = {};
    
    for (const property of objectNode.properties) {
      if (property.key.type === 'Identifier' && property.value.type === 'StringLiteral') {
        meta[property.key.name] = property.value.value;
      } else if (property.key.type === 'Identifier' && property.value.type === 'BooleanLiteral') {
        meta[property.key.name] = property.value.value;
      }
    }
    
    return meta;
  }

  /**
   * 使用 AI 解析路由
   */
  async parseWithAI() {
    try {
      for (const routerFile of this.routerFiles) {
        const content = await fs.readFile(routerFile.fullPath, 'utf8');
        const aiRoutes = await this.aiParseRoutes(content, routerFile.path);
        
        if (aiRoutes.length > 0) {
          this.routes.push(...aiRoutes);
        }
      }
    } catch (error) {
      console.log(chalk.yellow(`⚠️  AI 解析失败: ${error.message}`));
    }
  }

  /**
   * AI 解析路由代码
   */
  async aiParseRoutes(content, filePath) {
    const prompt = `
请分析以下 Vue Router 配置文件，提取所有路由路径信息：

文件路径: ${filePath}

代码内容:
\`\`\`javascript
${content}
\`\`\`

请以 JSON 格式返回路由信息，格式如下：
\`\`\`json
[
  {
    "path": "/",
    "name": "Home",
    "component": "Home",
    "meta": {},
    "children": []
  }
]
\`\`\`

注意：
1. 只提取有效的路由路径（不包括通配符路由如 * 或 /:pathMatch(.*)）
2. 包括嵌套路由的完整路径
3. 如果是动态路由，保留参数部分（如 /user/:id）
`;

    try {
      const response = await this.callAI(prompt, {
        context: {
          taskType: 'route-parsing',
          fileName: filePath
        }
      });

      // 尝试解析 JSON 响应
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        const routes = JSON.parse(jsonMatch[1]);
        return Array.isArray(routes) ? routes : [];
      }

      return [];
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  AI 解析路由失败 ${filePath}: ${error.message}`));
      }
      return [];
    }
  }

  /**
   * 获取所有可访问的路由路径
   */
  getAllRoutePaths() {
    const paths = [];
    
    const extractPaths = (routes, basePath = '') => {
      for (const route of routes) {
        if (route.path) {
          let fullPath = route.path;
          
          // 处理相对路径
          if (!route.path.startsWith('/') && basePath) {
            fullPath = basePath + '/' + route.path;
          } else if (!route.path.startsWith('/')) {
            fullPath = '/' + route.path;
          }
          
          // 跳过通配符路由
          if (!fullPath.includes('*') && !fullPath.includes(':pathMatch')) {
            paths.push({
              path: fullPath,
              name: route.name,
              meta: route.meta || {}
            });
          }
          
          // 处理子路由
          if (route.children && route.children.length > 0) {
            extractPaths(route.children, fullPath);
          }
        }
      }
    };
    
    extractPaths(this.routes);
    return paths;
  }

  /**
   * 打印解析结果
   */
  printRoutes() {
    console.log(chalk.blue('\n📋 解析到的路由:'));
    
    const paths = this.getAllRoutePaths();
    for (const route of paths) {
      console.log(chalk.gray(`   ${route.path}${route.name ? ` (${route.name})` : ''}`));
    }
  }

  /**
   * 获取解析结果
   */
  getRoutes() {
    return this.routes;
  }

  /**
   * 获取所有路由路径
   */
  getRoutePaths() {
    return this.getAllRoutePaths();
  }

  /**
   * 获取解析错误
   */
  getErrors() {
    return this.parseErrors;
  }
}

module.exports = RouteParser;
